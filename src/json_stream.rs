use duckdb::{
    core::{DataChunkHandle, Inser<PERSON>, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
};
use indexmap::IndexMap;
use std::{
    collections::HashMap,
    error::Error,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, Ordering},
};
use struson::reader::{JsonReader, JsonStreamReader, ValueType};
use thiserror::Error;

#[derive(Debug, Clone, PartialEq)]
pub enum PrimitiveType {
    Boolean,
    Integer,
    Double,
    String,
}

#[derive(Debug, Clone)]
pub enum JsonSchema {
    Primitive(PrimitiveType),
    Struct { 
        fields: IndexMap<String, JsonSchema> 
    },
    List { 
        element: Box<JsonSchema> 
    },
}

#[derive(Debug, <PERSON>lone)]
pub enum PNode<'s> {
    Leaf { 
        col_idx: usize 
    },
    Struct { 
        children: HashMap<&'s str, PNode<'s>>, 
        any: bool 
    },
    List { 
        elem: Box<PNode<'s>>, 
        required: bool 
    },
}

#[derive(Debug)]
pub struct Bitmap {
    bits: Vec<u64>,
    len: usize,
}

impl Bitmap {
    pub fn new(capacity: usize) -> Self {
        let words = (capacity + 63) / 64;
        Self {
            bits: vec![0; words],
            len: capacity,
        }
    }

    pub fn set_valid(&mut self, idx: usize) {
        if idx < self.len {
            let word_idx = idx / 64;
            let bit_idx = idx % 64;
            self.bits[word_idx] |= 1u64 << bit_idx;
        }
    }

    pub fn set_null(&mut self, idx: usize) {
        if idx < self.len {
            let word_idx = idx / 64;
            let bit_idx = idx % 64;
            self.bits[word_idx] &= !(1u64 << bit_idx);
        }
    }

    pub fn clear(&mut self) {
        self.bits.fill(0);
    }
}

#[derive(Debug)]
pub enum ColBuilder {
    Boolean {
        data: Vec<bool>,
        validity: Bitmap,
    },
    Integer {
        data: Vec<i64>,
        validity: Bitmap,
    },
    Double {
        data: Vec<f64>,
        validity: Bitmap,
    },
    String {
        data: Vec<String>,
        validity: Bitmap,
    },
    List {
        offsets: Vec<(u32, u32)>,
        elems: Box<ColBuilder>,
        validity: Bitmap,
    },
    Struct {
        children: Vec<ColBuilder>,
        validity: Bitmap,
    },
}

impl ColBuilder {
    pub fn new_boolean(capacity: usize) -> Self {
        Self::Boolean {
            data: Vec::with_capacity(capacity),
            validity: Bitmap::new(capacity),
        }
    }

    pub fn new_integer(capacity: usize) -> Self {
        Self::Integer {
            data: Vec::with_capacity(capacity),
            validity: Bitmap::new(capacity),
        }
    }

    pub fn new_double(capacity: usize) -> Self {
        Self::Double {
            data: Vec::with_capacity(capacity),
            validity: Bitmap::new(capacity),
        }
    }

    pub fn new_string(capacity: usize) -> Self {
        Self::String {
            data: Vec::with_capacity(capacity),
            validity: Bitmap::new(capacity),
        }
    }

    pub fn new_list(capacity: usize, elem_builder: ColBuilder) -> Self {
        Self::List {
            offsets: Vec::with_capacity(capacity),
            elems: Box::new(elem_builder),
            validity: Bitmap::new(capacity),
        }
    }

    pub fn new_struct(capacity: usize, children: Vec<ColBuilder>) -> Self {
        Self::Struct {
            children,
            validity: Bitmap::new(capacity),
        }
    }

    pub fn clear(&mut self) {
        match self {
            ColBuilder::Boolean { data, validity } => {
                data.clear();
                validity.clear();
            }
            ColBuilder::Integer { data, validity } => {
                data.clear();
                validity.clear();
            }
            ColBuilder::Double { data, validity } => {
                data.clear();
                validity.clear();
            }
            ColBuilder::String { data, validity } => {
                data.clear();
                validity.clear();
            }
            ColBuilder::List { offsets, elems, validity } => {
                offsets.clear();
                elems.clear();
                validity.clear();
            }
            ColBuilder::Struct { children, validity } => {
                for child in children {
                    child.clear();
                }
                validity.clear();
            }
        }
    }
}

pub struct ChunkBuilder {
    pub rows_cap: usize,
    pub rows: usize,
    pub cols: Vec<ColBuilder>,
}

impl ChunkBuilder {
    pub fn new(rows_cap: usize, schema: &JsonSchema) -> Self {
        let cols = vec![Self::create_col_builder(rows_cap, schema)];
        Self {
            rows_cap,
            rows: 0,
            cols,
        }
    }

    fn create_col_builder(capacity: usize, schema: &JsonSchema) -> ColBuilder {
        match schema {
            JsonSchema::Primitive(PrimitiveType::Boolean) => ColBuilder::new_boolean(capacity),
            JsonSchema::Primitive(PrimitiveType::Integer) => ColBuilder::new_integer(capacity),
            JsonSchema::Primitive(PrimitiveType::Double) => ColBuilder::new_double(capacity),
            JsonSchema::Primitive(PrimitiveType::String) => ColBuilder::new_string(capacity),
            JsonSchema::List { element } => {
                let elem_builder = Self::create_col_builder(0, element);
                ColBuilder::new_list(capacity, elem_builder)
            }
            JsonSchema::Struct { fields } => {
                let children: Vec<ColBuilder> = fields
                    .values()
                    .map(|field_schema| Self::create_col_builder(capacity, field_schema))
                    .collect();
                ColBuilder::new_struct(capacity, children)
            }
        }
    }

    pub fn is_full(&self) -> bool {
        self.rows >= self.rows_cap
    }

    pub fn clear(&mut self) {
        self.rows = 0;
        for col in &mut self.cols {
            col.clear();
        }
    }
}

#[derive(Error, Debug)]
pub enum JsonStreamError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("JSON parsing error: {0}")]
    Json(#[from] struson::reader::ReaderError),
    #[error("Schema error: {0}")]
    Schema(String),
    #[error("Type mismatch: expected {expected}, found {found}")]
    TypeMismatch { expected: String, found: String },
}

pub struct SchemaDetector {
    max_samples: usize,
}

impl SchemaDetector {
    pub fn new(max_samples: usize) -> Self {
        Self { max_samples }
    }

    pub fn detect_schema<P: AsRef<Path>>(&self, file_path: P) -> Result<JsonSchema, JsonStreamError> {
        let file = File::open(file_path)?;
        let reader = BufReader::new(file);
        let mut json_reader = JsonStreamReader::new(reader);

        let mut samples = Vec::new();
        let mut count = 0;

        // Try to read multiple JSON objects from the file
        while count < self.max_samples {
            match json_reader.peek() {
                Ok(value_type) => {
                    let schema = self.infer_value_schema(&mut json_reader)?;
                    samples.push(schema);
                    count += 1;
                }
                Err(_) => break, // End of file or error
            }
        }

        if samples.is_empty() {
            return Err(JsonStreamError::Schema("No JSON objects found in file".to_string()));
        }

        // Merge all sample schemas into one
        let merged = samples.into_iter().next().unwrap();
        // For now, just return the first schema - we'll implement merging later
        Ok(merged)
    }

    fn infer_value_schema(&self, reader: &mut JsonStreamReader<BufReader<File>>) -> Result<JsonSchema, JsonStreamError> {
        match reader.peek()? {
            ValueType::Object => {
                reader.begin_object()?;
                let mut fields = IndexMap::new();

                while reader.has_next()? {
                    let key = reader.next_name()?.to_string();
                    let value_schema = self.infer_value_schema(reader)?;
                    fields.insert(key, value_schema);
                }

                reader.end_object()?;
                Ok(JsonSchema::Struct { fields })
            }
            ValueType::Array => {
                reader.begin_array()?;

                // Sample first element to determine array element type
                let element_schema = if reader.has_next()? {
                    self.infer_value_schema(reader)?
                } else {
                    // Empty array, default to string
                    JsonSchema::Primitive(PrimitiveType::String)
                };

                // Skip remaining elements for now
                while reader.has_next()? {
                    reader.skip_value()?;
                }

                reader.end_array()?;
                Ok(JsonSchema::List { element: Box::new(element_schema) })
            }
            ValueType::String => {
                reader.next_string()?;
                Ok(JsonSchema::Primitive(PrimitiveType::String))
            }
            ValueType::Number => {
                let number_str = reader.next_number_as_string()?;
                // Simple heuristic: if it contains a dot, it's a double
                if number_str.contains('.') {
                    Ok(JsonSchema::Primitive(PrimitiveType::Double))
                } else {
                    Ok(JsonSchema::Primitive(PrimitiveType::Integer))
                }
            }
            ValueType::Boolean => {
                reader.next_bool()?;
                Ok(JsonSchema::Primitive(PrimitiveType::Boolean))
            }
            ValueType::Null => {
                reader.next_null()?;
                // Default null to string type
                Ok(JsonSchema::Primitive(PrimitiveType::String))
            }
        }
    }
}

#[repr(C)]
pub struct JsonStreamBindData {
    pub file_path: String,
    pub schema: JsonSchema,
}

#[repr(C)]
pub struct JsonStreamInitData {
    pub done: AtomicBool,
    pub chunks: Vec<ChunkBuilder>,
    pub current_chunk: usize,
}

pub struct JsonStreamVTab;

impl JsonStreamVTab {
    fn add_columns_from_schema(
        bind: &BindInfo,
        schema: &JsonSchema,
        prefix: &str,
    ) -> Result<(), Box<dyn Error>> {
        match schema {
            JsonSchema::Primitive(ptype) => {
                let logical_type = match ptype {
                    PrimitiveType::Boolean => LogicalTypeHandle::from(LogicalTypeId::Boolean),
                    PrimitiveType::Integer => LogicalTypeHandle::from(LogicalTypeId::Bigint),
                    PrimitiveType::Double => LogicalTypeHandle::from(LogicalTypeId::Double),
                    PrimitiveType::String => LogicalTypeHandle::from(LogicalTypeId::Varchar),
                };
                let col_name = if prefix.is_empty() { "value".to_string() } else { prefix.to_string() };
                bind.add_result_column(&col_name, logical_type);
            }
            JsonSchema::Struct { fields } => {
                for (field_name, field_schema) in fields {
                    let col_name = if prefix.is_empty() {
                        field_name.clone()
                    } else {
                        format!("{}.{}", prefix, field_name)
                    };
                    Self::add_columns_from_schema(bind, field_schema, &col_name)?;
                }
            }
            JsonSchema::List { element } => {
                // For lists, we'll create a single column with LIST type
                // For now, just create a VARCHAR column as a placeholder
                let col_name = if prefix.is_empty() { "list".to_string() } else { prefix.to_string() };
                bind.add_result_column(&col_name, LogicalTypeHandle::from(LogicalTypeId::Varchar));
            }
        }
        Ok(())
    }
}

pub struct ProjectionTreeBuilder<'s> {
    schema: &'s JsonSchema,
}

impl<'s> ProjectionTreeBuilder<'s> {
    pub fn new(schema: &'s JsonSchema) -> Self {
        Self { schema }
    }

    pub fn build_projection_tree(
        &self,
        projected_columns: &[String],
    ) -> Result<PNode<'s>, JsonStreamError> {
        // If no columns are projected, project everything
        if projected_columns.is_empty() {
            return self.build_full_projection_tree(self.schema);
        }

        // Build a projection tree based on the requested columns
        self.build_selective_projection_tree(self.schema, projected_columns, "")
    }

    fn build_full_projection_tree(&self, schema: &'s JsonSchema) -> Result<PNode<'s>, JsonStreamError> {
        match schema {
            JsonSchema::Primitive(_) => Ok(PNode::Leaf { col_idx: 0 }),
            JsonSchema::Struct { fields } => {
                let mut children = HashMap::new();
                for (field_name, field_schema) in fields {
                    let child_node = self.build_full_projection_tree(field_schema)?;
                    children.insert(field_name.as_str(), child_node);
                }
                Ok(PNode::Struct { children, any: true })
            }
            JsonSchema::List { element } => {
                let elem_node = self.build_full_projection_tree(element)?;
                Ok(PNode::List {
                    elem: Box::new(elem_node),
                    required: true,
                })
            }
        }
    }

    fn build_selective_projection_tree(
        &self,
        schema: &'s JsonSchema,
        projected_columns: &[String],
        current_path: &str,
    ) -> Result<PNode<'s>, JsonStreamError> {
        match schema {
            JsonSchema::Primitive(_) => {
                // Check if this primitive is in the projected columns
                let is_projected = projected_columns.iter().any(|col| {
                    col == current_path || (current_path.is_empty() && col == "value")
                });

                if is_projected {
                    Ok(PNode::Leaf { col_idx: 0 }) // We'll assign proper indices later
                } else {
                    // This shouldn't happen in a well-formed projection, but handle gracefully
                    Ok(PNode::Leaf { col_idx: 0 })
                }
            }
            JsonSchema::Struct { fields } => {
                let mut children = HashMap::new();
                let mut any_child_projected = false;

                for (field_name, field_schema) in fields {
                    let field_path = if current_path.is_empty() {
                        field_name.clone()
                    } else {
                        format!("{}.{}", current_path, field_name)
                    };

                    // Check if this field or any of its children are projected
                    let field_is_projected = projected_columns.iter().any(|col| {
                        col.starts_with(&field_path)
                    });

                    if field_is_projected {
                        let child_node = self.build_selective_projection_tree(
                            field_schema,
                            projected_columns,
                            &field_path,
                        )?;
                        children.insert(field_name.as_str(), child_node);
                        any_child_projected = true;
                    }
                }

                Ok(PNode::Struct {
                    children,
                    any: any_child_projected,
                })
            }
            JsonSchema::List { element } => {
                // Check if this list or its elements are projected
                let list_is_projected = projected_columns.iter().any(|col| {
                    col.starts_with(current_path)
                });

                if list_is_projected {
                    let elem_node = self.build_selective_projection_tree(
                        element,
                        projected_columns,
                        current_path, // Lists don't change the path for their elements
                    )?;
                    Ok(PNode::List {
                        elem: Box::new(elem_node),
                        required: true,
                    })
                } else {
                    Ok(PNode::List {
                        elem: Box::new(PNode::Leaf { col_idx: 0 }),
                        required: false,
                    })
                }
            }
        }
    }
}

pub struct StreamingJsonParser<'s> {
    projection_tree: PNode<'s>,
    chunk_builder: ChunkBuilder,
}

impl<'s> StreamingJsonParser<'s> {
    pub fn new(projection_tree: PNode<'s>, schema: &JsonSchema, chunk_size: usize) -> Self {
        let chunk_builder = ChunkBuilder::new(chunk_size, schema);
        Self {
            projection_tree,
            chunk_builder,
        }
    }

    pub fn parse_file<P: AsRef<Path>>(&mut self, file_path: P) -> Result<Vec<ChunkBuilder>, JsonStreamError> {
        let file = File::open(file_path)?;
        let reader = BufReader::new(file);
        let mut json_reader = JsonStreamReader::new(reader);

        let mut chunks = Vec::new();

        // Parse JSON objects one by one
        loop {
            match json_reader.peek() {
                Ok(_) => {
                    // Clone the projection tree to avoid borrowing issues
                    let projection_tree = self.projection_tree.clone();
                    self.parse_object(&mut json_reader, &projection_tree)?;

                    if self.chunk_builder.is_full() {
                        let mut new_chunk = ChunkBuilder::new(
                            self.chunk_builder.rows_cap,
                            &JsonSchema::Primitive(PrimitiveType::String)
                        );
                        std::mem::swap(&mut self.chunk_builder, &mut new_chunk);
                        chunks.push(new_chunk);
                    }
                }
                Err(_) => break, // End of file or error
            }
        }

        // Add final chunk if it has data
        if self.chunk_builder.rows > 0 {
            let mut final_chunk = ChunkBuilder::new(
                self.chunk_builder.rows_cap,
                &JsonSchema::Primitive(PrimitiveType::String)
            );
            std::mem::swap(&mut self.chunk_builder, &mut final_chunk);
            chunks.push(final_chunk);
        }

        Ok(chunks)
    }

    fn parse_object(
        &mut self,
        reader: &mut JsonStreamReader<BufReader<File>>,
        node: &PNode<'s>,
    ) -> Result<(), JsonStreamError> {
        match node {
            PNode::Leaf { col_idx: _ } => {
                // Parse primitive value
                self.parse_primitive_value(reader)?;
            }
            PNode::Struct { children, any } => {
                if *any {
                    self.parse_struct_object(reader, children)?;
                } else {
                    // Skip entire object
                    reader.skip_value()?;
                }
            }
            PNode::List { elem, required } => {
                if *required {
                    self.parse_list_array(reader, elem)?;
                } else {
                    // Skip entire array
                    reader.skip_value()?;
                }
            }
        }
        Ok(())
    }

    fn parse_struct_object(
        &mut self,
        reader: &mut JsonStreamReader<BufReader<File>>,
        children: &HashMap<&'s str, PNode<'s>>,
    ) -> Result<(), JsonStreamError> {
        reader.begin_object()?;

        while reader.has_next()? {
            let key = reader.next_name()?;

            if let Some(child_node) = children.get(key) {
                // Parse this field
                self.parse_object(reader, child_node)?;
            } else {
                // Skip this field
                reader.skip_value()?;
            }
        }

        reader.end_object()?;
        Ok(())
    }

    fn parse_list_array(
        &mut self,
        reader: &mut JsonStreamReader<BufReader<File>>,
        elem_node: &PNode<'s>,
    ) -> Result<(), JsonStreamError> {
        reader.begin_array()?;

        while reader.has_next()? {
            self.parse_object(reader, elem_node)?;
        }

        reader.end_array()?;
        Ok(())
    }

    fn parse_primitive_value(
        &mut self,
        reader: &mut JsonStreamReader<BufReader<File>>,
    ) -> Result<(), JsonStreamError> {
        match reader.peek()? {
            ValueType::String => {
                let value = reader.next_string()?;
                self.add_string_value(value);
            }
            ValueType::Number => {
                let number_str = reader.next_number_as_string()?;
                if number_str.contains('.') {
                    if let Ok(value) = number_str.parse::<f64>() {
                        self.add_double_value(value);
                    } else {
                        self.add_null_value();
                    }
                } else {
                    if let Ok(value) = number_str.parse::<i64>() {
                        self.add_integer_value(value);
                    } else {
                        self.add_null_value();
                    }
                }
            }
            ValueType::Boolean => {
                let value = reader.next_bool()?;
                self.add_boolean_value(value);
            }
            ValueType::Null => {
                reader.next_null()?;
                self.add_null_value();
            }
            ValueType::Object => {
                // This shouldn't happen for a primitive, but handle gracefully
                reader.skip_value()?;
                self.add_null_value();
            }
            ValueType::Array => {
                // This shouldn't happen for a primitive, but handle gracefully
                reader.skip_value()?;
                self.add_null_value();
            }
        }
        Ok(())
    }

    fn add_string_value(&mut self, value: String) {
        if let Some(col) = self.chunk_builder.cols.get_mut(0) {
            match col {
                ColBuilder::String { data, validity } => {
                    data.push(value);
                    validity.set_valid(self.chunk_builder.rows);
                }
                _ => {
                    // Type mismatch, set null
                    self.set_null_in_column(0);
                }
            }
        }
        self.chunk_builder.rows += 1;
    }

    fn add_integer_value(&mut self, value: i64) {
        if let Some(col) = self.chunk_builder.cols.get_mut(0) {
            match col {
                ColBuilder::Integer { data, validity } => {
                    data.push(value);
                    validity.set_valid(self.chunk_builder.rows);
                }
                _ => {
                    // Type mismatch, set null
                    self.set_null_in_column(0);
                }
            }
        }
        self.chunk_builder.rows += 1;
    }

    fn add_double_value(&mut self, value: f64) {
        if let Some(col) = self.chunk_builder.cols.get_mut(0) {
            match col {
                ColBuilder::Double { data, validity } => {
                    data.push(value);
                    validity.set_valid(self.chunk_builder.rows);
                }
                _ => {
                    // Type mismatch, set null
                    self.set_null_in_column(0);
                }
            }
        }
        self.chunk_builder.rows += 1;
    }

    fn add_boolean_value(&mut self, value: bool) {
        if let Some(col) = self.chunk_builder.cols.get_mut(0) {
            match col {
                ColBuilder::Boolean { data, validity } => {
                    data.push(value);
                    validity.set_valid(self.chunk_builder.rows);
                }
                _ => {
                    // Type mismatch, set null
                    self.set_null_in_column(0);
                }
            }
        }
        self.chunk_builder.rows += 1;
    }

    fn add_null_value(&mut self) {
        self.set_null_in_column(0);
        self.chunk_builder.rows += 1;
    }

    fn set_null_in_column(&mut self, col_idx: usize) {
        let current_row = self.chunk_builder.rows;
        if let Some(col) = self.chunk_builder.cols.get_mut(col_idx) {
            Self::set_null_in_col_builder(col, current_row);
        }
    }

    fn set_null_in_col_builder(col: &mut ColBuilder, row_idx: usize) {
        match col {
            ColBuilder::Boolean { data, validity } => {
                data.push(false); // Default value
                validity.set_null(row_idx);
            }
            ColBuilder::Integer { data, validity } => {
                data.push(0); // Default value
                validity.set_null(row_idx);
            }
            ColBuilder::Double { data, validity } => {
                data.push(0.0); // Default value
                validity.set_null(row_idx);
            }
            ColBuilder::String { data, validity } => {
                data.push(String::new()); // Default value
                validity.set_null(row_idx);
            }
            ColBuilder::List { offsets, validity, .. } => {
                offsets.push((0, 0)); // Empty list
                validity.set_null(row_idx);
            }
            ColBuilder::Struct { children, validity } => {
                // Set null in all children
                for child in children.iter_mut() {
                    Self::set_null_in_col_builder(child, row_idx);
                }
                validity.set_null(row_idx);
            }
        }
    }
}

pub struct VectorPopulator;

impl VectorPopulator {
    pub fn populate_chunk(
        chunk_builder: &ChunkBuilder,
        output: &mut DataChunkHandle,
    ) -> Result<(), JsonStreamError> {
        // Set the number of rows in the output chunk
        output.set_len(chunk_builder.rows);

        // Populate each column
        for (col_idx, col_builder) in chunk_builder.cols.iter().enumerate() {
            Self::populate_column(col_builder, output, col_idx)?;
        }

        Ok(())
    }

    fn populate_column(
        col_builder: &ColBuilder,
        output: &mut DataChunkHandle,
        col_idx: usize,
    ) -> Result<(), JsonStreamError> {
        match col_builder {
            ColBuilder::Boolean { data, validity } => {
                let mut vector = output.flat_vector(col_idx);
                Self::populate_boolean_vector(&mut vector, data, validity)?;
            }
            ColBuilder::Integer { data, validity } => {
                let mut vector = output.flat_vector(col_idx);
                Self::populate_integer_vector(&mut vector, data, validity)?;
            }
            ColBuilder::Double { data, validity } => {
                let mut vector = output.flat_vector(col_idx);
                Self::populate_double_vector(&mut vector, data, validity)?;
            }
            ColBuilder::String { data, validity } => {
                let mut vector = output.flat_vector(col_idx);
                Self::populate_string_vector(&mut vector, data, validity)?;
            }
            ColBuilder::List { offsets, elems, validity } => {
                let mut vector = output.list_vector(col_idx);
                Self::populate_list_vector(&mut vector, offsets, elems, validity)?;
            }
            ColBuilder::Struct { children, validity } => {
                let mut vector = output.struct_vector(col_idx);
                Self::populate_struct_vector(&mut vector, children, validity)?;
            }
        }
        Ok(())
    }

    fn populate_boolean_vector(
        vector: &mut duckdb::core::FlatVector,
        data: &[bool],
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // For now, convert booleans to strings as a workaround
        for (i, &value) in data.iter().enumerate() {
            if Self::is_valid(validity, i) {
                let bool_str = if value { "true" } else { "false" };
                vector.insert(i, bool_str);
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }

    fn populate_integer_vector(
        vector: &mut duckdb::core::FlatVector,
        data: &[i64],
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // Convert integers to strings as a workaround
        for (i, &value) in data.iter().enumerate() {
            if Self::is_valid(validity, i) {
                let int_str = value.to_string();
                vector.insert(i, int_str.as_str());
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }

    fn populate_double_vector(
        vector: &mut duckdb::core::FlatVector,
        data: &[f64],
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // Convert doubles to strings as a workaround
        for (i, &value) in data.iter().enumerate() {
            if Self::is_valid(validity, i) {
                let double_str = value.to_string();
                vector.insert(i, double_str.as_str());
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }

    fn populate_string_vector(
        vector: &mut duckdb::core::FlatVector,
        data: &[String],
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        for (i, value) in data.iter().enumerate() {
            if Self::is_valid(validity, i) {
                let c_string = std::ffi::CString::new(value.as_str())
                    .map_err(|_| JsonStreamError::Schema("Invalid string data".to_string()))?;
                vector.insert(i, c_string);
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }

    fn populate_list_vector(
        _vector: &duckdb::core::ListVector,
        _offsets: &[(u32, u32)],
        _elems: &ColBuilder,
        _validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // TODO: Implement list vector population
        // This is complex and requires proper DuckDB list vector API usage
        Ok(())
    }

    fn populate_struct_vector(
        _vector: &duckdb::core::StructVector,
        _children: &[ColBuilder],
        _validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // TODO: Implement struct vector population
        // This requires populating child vectors and setting parent validity
        Ok(())
    }

    fn is_valid(validity: &Bitmap, idx: usize) -> bool {
        if idx >= validity.len {
            return false;
        }
        let word_idx = idx / 64;
        let bit_idx = idx % 64;
        if word_idx >= validity.bits.len() {
            return false;
        }
        (validity.bits[word_idx] & (1u64 << bit_idx)) != 0
    }
}

impl VTab for JsonStreamVTab {
    type InitData = JsonStreamInitData;
    type BindData = JsonStreamBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn Error>> {
        let file_path = bind.get_parameter(0).to_string();

        // Detect schema from the file
        let detector = SchemaDetector::new(10); // Sample up to 10 objects
        let schema = detector.detect_schema(&file_path)?;

        // Add result columns based on detected schema
        Self::add_columns_from_schema(bind, &schema, "")?;

        Ok(JsonStreamBindData { file_path, schema })
    }

    fn init(_: &InitInfo) -> Result<Self::InitData, Box<dyn Error>> {
        Ok(JsonStreamInitData {
            done: AtomicBool::new(false),
        })
    }

    fn func(
        func: &TableFunctionInfo<Self>,
        output: &mut DataChunkHandle,
    ) -> Result<(), Box<dyn Error>> {
        let init_data = func.get_init_data();
        let _bind_data = func.get_bind_data();

        if init_data.done.swap(true, Ordering::Relaxed) {
            output.set_len(0);
        } else {
            // Placeholder implementation - will be replaced with actual streaming logic
            let vector = output.flat_vector(0);
            vector.insert(0, std::ffi::CString::new("JSON streaming placeholder")?);
            output.set_len(1);
        }
        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}
