# Streaming JSON DuckDB Extension in Rust - Complete Implementation Guide

## 0. High-Level Goals

| ID  | Requirement |
|-----|-------------|
| G-1 | Stream multi-GB JSON with memory ≈ chunk_rows × avg_row_size |
| G-2 | Projection push-down: materialize only selected paths |
| G-3 | Preserve LIST / STRUCT / primitive types (no VARCHAR fall-back) |
| G-4 | No hard maximum_object_size; default guard = u64::MAX |
| G-5 | Parallel scan of top-level array slices |
| G-6 | SQL UX: json_stream(path, …options…) table function |

## 1. Dependencies (Verified Versions)

Based on the extension-template-rs at commit b342ef0, use these exact versions:

| Crate | Version | Notes |
|-------|---------|-------|
| duckdb | 1.3.1 | Matches DuckDB v1.3.1 binary ABI |
| duckdb-loadable-macros | 0.1.5 | Required for extension macros |
| libduckdb-sys | 1.3.1 | Low-level FFI bindings |
| struson | 0.5 | Pull-based JSON stream, MIT license |
| indexmap | 2.0 | Preserve field order in schema |
| thiserror | 1.0 | Ergonomic error handling |

### Cargo Configuration

Add struson, indexmap and thiserror to the cargo dependencies

## 2. Build System (extension-template-rs)

### Build Commands

```bash
make debug          # Debug build with symbols
make release        # Optimized release build
make test_debug     # Run test suite (debug)
make test_release   # Run test suite (release)
make clean          # Clean build artifacts
```

## 3. DuckDB Rust API Reference

### 3.1 Core Imports and Types

```rust
use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
```

### 3.2 Table Function Trait Implementation

```rust
struct JsonStreamVTab;

impl VTab for JsonStreamVTab {
    type InitData = JsonStreamInitData;
    type BindData = JsonStreamBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn Error>> {
        // 1. Parse function parameters
        let file_path = bind.get_parameter(0).to_string();
        
        // 2. Detect JSON schema from file
        let schema = detect_schema(&file_path)?;
        
        // 3. Add result columns based on schema
        add_columns_from_schema(bind, &schema)?;
        
        Ok(JsonStreamBindData { file_path, schema })
    }

    fn init(_: &InitInfo) -> Result<Self::InitData, Box<dyn Error>> {
        Ok(JsonStreamInitData {
            done: AtomicBool::new(false),
            chunks: Vec::new(),
            current_chunk: 0,
        })
    }

    fn func(
        func: &TableFunctionInfo<Self>,
        output: &mut DataChunkHandle,
    ) -> Result<(), Box<dyn Error>> {
        // Main streaming logic implementation
        // Returns chunks of data until exhausted
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}
```

### 3.3 Required Data Structures

All bind/init data structures  use `#[repr(C)]`:

```rust
#[repr(C)]
pub struct JsonStreamBindData {
    pub file_path: String,
    pub schema: JsonSchema,
    pub projection_tree: Option<ProjectionTree>,
}

#[repr(C)]
pub struct JsonStreamInitData {
    pub done: AtomicBool,
    pub chunks: Vec<ChunkBuilder>,
    pub current_chunk: usize,
}
```

### 3.4 Vector Population Patterns

The DuckDB Rust API has limited type support. Here are the working patterns:

```rust
// String vectors (most reliable)
let vector = output.flat_vector(col_idx);
let c_string = std::ffi::CString::new(value)?;
vector.insert(row, c_string);

// For other types, convert to string as workaround initially
let vector = output.flat_vector(col_idx);
vector.insert(row, value.to_string().as_str());

// Set null values
vector.set_null(row);

// Set chunk length
output.set_len(row_count);
```

### 3.5 Extension Registration

```rust
#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonStreamVTab>("json_stream")
        .expect("Failed to register json_stream table function");
    Ok(())
}
```

### 4.2 Validity Bitmap Implementation

```rust
#[derive(Debug)]
pub struct Bitmap {
    bits: Vec<u64>,
    len: usize,
}

impl Bitmap {
    pub fn new(capacity: usize) -> Self {
        let words = (capacity + 63) / 64;
        Self {
            bits: vec![0; words],
            len: capacity,
        }
    }

    pub fn set_valid(&mut self, idx: usize) {
        if idx < self.len {
            let word_idx = idx / 64;
            let bit_idx = idx % 64;
            self.bits[word_idx] |= 1u64 << bit_idx;
        }
    }

    pub fn set_null(&mut self, idx: usize) {
        if idx < self.len {
            let word_idx = idx / 64;
            let bit_idx = idx % 64;
            self.bits[word_idx] &= !(1u64 << bit_idx);
        }
    }

    pub fn is_valid(&self, idx: usize) -> bool {
        if idx >= self.len { return false; }
        let word_idx = idx / 64;
        let bit_idx = idx % 64;
        if word_idx >= self.bits.len() { return false; }
        (self.bits[word_idx] & (1u64 << bit_idx)) != 0
    }

    pub fn clear(&mut self) {
        self.bits.fill(0);
    }
}
```

### 4.3 Column Builder Architecture

```rust
#[derive(Debug)]
pub enum ColBuilder {
    Boolean {
        data: Vec<bool>,
        validity: Bitmap,
    },
    Integer {
        data: Vec<i64>,
        validity: Bitmap,
    },
    Double {
        data: Vec<f64>,
        validity: Bitmap,
    },
    String {
        data: Vec<String>,
        validity: Bitmap,
    },
    List {
        offsets: Vec<(u32, u32)>,  // (start_offset, length) pairs
        elements: Box<ColBuilder>,  // Child builder for list elements
        validity: Bitmap,
    },
    Struct {
        children: Vec<ColBuilder>,  // One builder per struct field
        validity: Bitmap,
    },
}

impl ColBuilder {
    pub fn new_for_schema(capacity: usize, schema: &JsonSchema) -> Self {
        match schema {
            JsonSchema::Primitive(PrimitiveType::Boolean) => {
                Self::Boolean {
                    data: Vec::with_capacity(capacity),
                    validity: Bitmap::new(capacity),
                }
            }
            JsonSchema::Primitive(PrimitiveType::Integer) => {
                Self::Integer {
                    data: Vec::with_capacity(capacity),
                    validity: Bitmap::new(capacity),
                }
            }
            JsonSchema::Primitive(PrimitiveType::Double) => {
                Self::Double {
                    data: Vec::with_capacity(capacity),
                    validity: Bitmap::new(capacity),
                }
            }
            JsonSchema::Primitive(PrimitiveType::String) => {
                Self::String {
                    data: Vec::with_capacity(capacity),
                    validity: Bitmap::new(capacity),
                }
            }
            JsonSchema::List { element } => {
                let element_builder = Self::new_for_schema(0, element); // Start with 0, will grow
                Self::List {
                    offsets: Vec::with_capacity(capacity),
                    elements: Box::new(element_builder),
                    validity: Bitmap::new(capacity),
                }
            }
            JsonSchema::Struct { fields } => {
                let children: Vec<ColBuilder> = fields
                    .values()
                    .map(|field_schema| Self::new_for_schema(capacity, field_schema))
                    .collect();
                Self::Struct {
                    children,
                    validity: Bitmap::new(capacity),
                }
            }
        }
    }

    pub fn clear(&mut self) {
        match self {
            ColBuilder::Boolean { data, validity } => {
                data.clear();
                validity.clear();
            }
            ColBuilder::Integer { data, validity } => {
                data.clear();
                validity.clear();
            }
            ColBuilder::Double { data, validity } => {
                data.clear();
                validity.clear();
            }
            ColBuilder::String { data, validity } => {
                data.clear();
                validity.clear();
            }
            ColBuilder::List { offsets, elements, validity } => {
                offsets.clear();
                elements.clear();
                validity.clear();
            }
            ColBuilder::Struct { children, validity } => {
                for child in children {
                    child.clear();
                }
                validity.clear();
            }
        }
    }
}
```

### 4.4 Chunk Builder Implementation

```rust
pub struct ChunkBuilder {
    pub rows_capacity: usize,
    pub rows_count: usize,
    pub columns: Vec<ColBuilder>,
}

impl ChunkBuilder {
    pub fn new(rows_capacity: usize, schema: &JsonSchema) -> Self {
        // For root-level objects, create one column per top-level field
        let columns = match schema {
            JsonSchema::Struct { fields } => {
                fields.values()
                    .map(|field_schema| ColBuilder::new_for_schema(rows_capacity, field_schema))
                    .collect()
            }
            _ => {
                // Single column for primitive or list at root
                vec![ColBuilder::new_for_schema(rows_capacity, schema)]
            }
        };

        Self {
            rows_capacity,
            rows_count: 0,
            columns,
        }
    }

    pub fn is_full(&self) -> bool {
        self.rows_count >= self.rows_capacity
    }

    pub fn add_row(&mut self) -> usize {
        let row_idx = self.rows_count;
        self.rows_count += 1;
        row_idx
    }

    pub fn clear(&mut self) {
        self.rows_count = 0;
        for col in &mut self.columns {
            col.clear();
        }
    }
}
```

## 5. Struson JSON Streaming API

### 5.1 Correct API Usage Patterns

```rust
use struson::reader::{JsonStreamReader, ValueType};
use std::io::BufReader;
use std::fs::File;

// Initialize reader
let file = File::open(file_path)?;
let buf_reader = BufReader::new(file);
let mut json_reader = JsonStreamReader::new(buf_reader);

// Check value type before processing
match json_reader.peek()? {
    ValueType::Object => {
        json_reader.begin_object()?;
        while json_reader.has_next()? {
            let key = json_reader.next_name()?;
            // Process value based on projection tree
            if should_process_field(key) {
                process_value(&mut json_reader, projection_node)?;
            } else {
                json_reader.skip_value()?; // Efficient skip
            }
        }
        json_reader.end_object()?;
    }
    ValueType::Array => {
        json_reader.begin_array()?;
        while json_reader.has_next()? {
            process_array_element(&mut json_reader, projection_node)?;
        }
        json_reader.end_array()?;
    }
    ValueType::String => {
        let value = json_reader.next_string()?;
        // Store in appropriate column
    }
    ValueType::Number => {
        let number_str = json_reader.next_number_as_string()?;
        // Parse as i64 or f64 based on schema
    }
    ValueType::Boolean => {
        let value = json_reader.next_bool()?;
        // Store in boolean column
    }
    ValueType::Null => {
        json_reader.next_null()?;
        // Set validity bit to false
    }
}
```

### 5.2 Critical Struson Rules

1. **Never call `peek()` after consuming a value** - this causes panics
2. **Use `skip_value()` for efficient subtree skipping** - much faster than parsing
3. **Handle all ValueType variants** - missing cases cause runtime errors
4. **Proper nesting with begin/end calls** - unmatched calls cause errors

### 5.3 Schema Detection Implementation

```rust
pub struct SchemaDetector {
    max_samples: usize,
}

impl SchemaDetector {
    pub fn detect_schema<P: AsRef<Path>>(&self, file_path: P) -> Result<JsonSchema, JsonStreamError> {
        let file = File::open(file_path)?;
        let buf_reader = BufReader::new(file);
        let mut json_reader = JsonStreamReader::new(buf_reader);

        // Sample first JSON value to infer schema
        self.infer_value_schema(&mut json_reader)
    }

    fn infer_value_schema(&self, reader: &mut JsonStreamReader<BufReader<File>>) -> Result<JsonSchema, JsonStreamError> {
        match reader.peek()? {
            ValueType::Object => {
                reader.begin_object()?;
                let mut fields = IndexMap::new();

                while reader.has_next()? {
                    let key = reader.next_name()?.to_string();
                    let value_schema = self.infer_value_schema(reader)?;
                    fields.insert(key, value_schema);
                }

                reader.end_object()?;
                Ok(JsonSchema::Struct { fields })
            }
            ValueType::Array => {
                reader.begin_array()?;

                let element_schema = if reader.has_next()? {
                    self.infer_value_schema(reader)?
                } else {
                    JsonSchema::Primitive(PrimitiveType::String) // Default for empty arrays
                };

                // Skip remaining elements for schema detection
                while reader.has_next()? {
                    reader.skip_value()?;
                }

                reader.end_array()?;
                Ok(JsonSchema::List { element: Box::new(element_schema) })
            }
            ValueType::String => {
                reader.next_string()?;
                Ok(JsonSchema::Primitive(PrimitiveType::String))
            }
            ValueType::Number => {
                let number_str = reader.next_number_as_string()?;
                if number_str.contains('.') {
                    Ok(JsonSchema::Primitive(PrimitiveType::Double))
                } else {
                    Ok(JsonSchema::Primitive(PrimitiveType::Integer))
                }
            }
            ValueType::Boolean => {
                reader.next_bool()?;
                Ok(JsonSchema::Primitive(PrimitiveType::Boolean))
            }
            ValueType::Null => {
                reader.next_null()?;
                Ok(JsonSchema::Primitive(PrimitiveType::String)) // Default null to string
            }
        }
    }
}
```

## 6. Projection Tree Implementation

### 6.1 Projection Tree Builder

```rust
pub struct ProjectionTreeBuilder<'s> {
    schema: &'s JsonSchema,
}

impl<'s> ProjectionTreeBuilder<'s> {
    pub fn new(schema: &'s JsonSchema) -> Self {
        Self { schema }
    }

    pub fn build_projection_tree(
        &self,
        projected_columns: &[String],
    ) -> Result<ProjectionNode<'s>, JsonStreamError> {
        if projected_columns.is_empty() {
            // No projection specified, select all
            self.build_full_projection_tree(self.schema)
        } else {
            // Build selective projection tree
            self.build_selective_projection_tree(self.schema, projected_columns, "")
        }
    }

    fn build_full_projection_tree(&self, schema: &'s JsonSchema) -> Result<ProjectionNode<'s>, JsonStreamError> {
        match schema {
            JsonSchema::Primitive(ptype) => {
                let logical_type = match ptype {
                    PrimitiveType::Boolean => LogicalTypeId::Boolean,
                    PrimitiveType::Integer => LogicalTypeId::Bigint,
                    PrimitiveType::Double => LogicalTypeId::Double,
                    PrimitiveType::String => LogicalTypeId::Varchar,
                };
                Ok(ProjectionNode::Leaf { col_idx: 0, logical_type })
            }
            JsonSchema::Struct { fields } => {
                let mut children = HashMap::new();
                for (field_name, field_schema) in fields {
                    let child_node = self.build_full_projection_tree(field_schema)?;
                    children.insert(field_name.as_str(), child_node);
                }
                Ok(ProjectionNode::Struct { children, any_projected: true })
            }
            JsonSchema::List { element } => {
                let element_node = self.build_full_projection_tree(element)?;
                Ok(ProjectionNode::List {
                    element: Box::new(element_node),
                    required: true,
                })
            }
        }
    }

    fn build_selective_projection_tree(
        &self,
        schema: &'s JsonSchema,
        projected_columns: &[String],
        current_path: &str,
    ) -> Result<ProjectionNode<'s>, JsonStreamError> {
        match schema {
            JsonSchema::Primitive(ptype) => {
                let is_projected = projected_columns.iter().any(|col| {
                    col == current_path || (current_path.is_empty() && col == "value")
                });

                if is_projected {
                    let logical_type = match ptype {
                        PrimitiveType::Boolean => LogicalTypeId::Boolean,
                        PrimitiveType::Integer => LogicalTypeId::Bigint,
                        PrimitiveType::Double => LogicalTypeId::Double,
                        PrimitiveType::String => LogicalTypeId::Varchar,
                    };
                    Ok(ProjectionNode::Leaf { col_idx: 0, logical_type })
                } else {
                    // Not projected, but still need a node for structure
                    Ok(ProjectionNode::Leaf { col_idx: usize::MAX, logical_type: LogicalTypeId::Varchar })
                }
            }
            JsonSchema::Struct { fields } => {
                let mut children = HashMap::new();
                let mut any_projected = false;

                for (field_name, field_schema) in fields {
                    let field_path = if current_path.is_empty() {
                        field_name.clone()
                    } else {
                        format!("{}.{}", current_path, field_name)
                    };

                    let field_is_projected = projected_columns.iter().any(|col| {
                        col.starts_with(&field_path)
                    });

                    if field_is_projected {
                        let child_node = self.build_selective_projection_tree(
                            field_schema,
                            projected_columns,
                            &field_path,
                        )?;
                        children.insert(field_name.as_str(), child_node);
                        any_projected = true;
                    }
                }

                Ok(ProjectionNode::Struct { children, any_projected })
            }
            JsonSchema::List { element } => {
                let list_is_projected = projected_columns.iter().any(|col| {
                    col.starts_with(current_path)
                });

                if list_is_projected {
                    let element_node = self.build_selective_projection_tree(
                        element,
                        projected_columns,
                        current_path,
                    )?;
                    Ok(ProjectionNode::List {
                        element: Box::new(element_node),
                        required: true,
                    })
                } else {
                    Ok(ProjectionNode::List {
                        element: Box::new(ProjectionNode::Leaf {
                            col_idx: usize::MAX,
                            logical_type: LogicalTypeId::Varchar
                        }),
                        required: false,
                    })
                }
            }
        }
    }
}
```

## 7. Streaming Parser Implementation

### 7.1 Core Parser Structure

```rust
pub struct StreamingJsonParser<'s> {
    projection_tree: ProjectionNode<'s>,
    chunk_builder: ChunkBuilder,
}

impl<'s> StreamingJsonParser<'s> {
    pub fn new(
        projection_tree: ProjectionNode<'s>,
        schema: &JsonSchema,
        chunk_size: usize,
    ) -> Self {
        let chunk_builder = ChunkBuilder::new(chunk_size, schema);
        Self {
            projection_tree,
            chunk_builder,
        }
    }

    pub fn parse_file<P: AsRef<Path>>(
        &mut self,
        file_path: P,
    ) -> Result<Vec<ChunkBuilder>, JsonStreamError> {
        let file = File::open(file_path)?;
        let buf_reader = BufReader::new(file);
        let mut json_reader = JsonStreamReader::new(buf_reader);

        let mut chunks = Vec::new();

        // Handle different root types
        match json_reader.peek()? {
            ValueType::Array => {
                // Root is array of objects
                json_reader.begin_array()?;
                while json_reader.has_next()? {
                    self.parse_value(&mut json_reader, &self.projection_tree)?;

                    if self.chunk_builder.is_full() {
                        chunks.push(self.flush_chunk()?);
                    }
                }
                json_reader.end_array()?;
            }
            _ => {
                // Single root object
                self.parse_value(&mut json_reader, &self.projection_tree)?;
            }
        }

        // Flush final chunk if it has data
        if self.chunk_builder.rows_count > 0 {
            chunks.push(self.flush_chunk()?);
        }

        Ok(chunks)
    }

    fn parse_value(
        &mut self,
        reader: &mut JsonStreamReader<BufReader<File>>,
        node: &ProjectionNode<'s>,
    ) -> Result<(), JsonStreamError> {
        match node {
            ProjectionNode::Leaf { col_idx, logical_type } => {
                if *col_idx == usize::MAX {
                    // Not projected, skip
                    reader.skip_value()?;
                } else {
                    self.parse_primitive_value(reader, *col_idx, *logical_type)?;
                }
            }
            ProjectionNode::Struct { children, any_projected } => {
                if !any_projected {
                    reader.skip_value()?;
                } else {
                    self.parse_struct_object(reader, children)?;
                }
            }
            ProjectionNode::List { element, required } => {
                if !required {
                    reader.skip_value()?;
                } else {
                    self.parse_list_array(reader, element)?;
                }
            }
        }
        Ok(())
    }

    fn parse_struct_object(
        &mut self,
        reader: &mut JsonStreamReader<BufReader<File>>,
        children: &HashMap<&'s str, ProjectionNode<'s>>,
    ) -> Result<(), JsonStreamError> {
        reader.begin_object()?;

        let row_idx = self.chunk_builder.add_row();

        while reader.has_next()? {
            let key = reader.next_name()?;

            if let Some(child_node) = children.get(key) {
                self.parse_value(reader, child_node)?;
            } else {
                reader.skip_value()?;
            }
        }

        reader.end_object()?;
        Ok(())
    }

    fn parse_list_array(
        &mut self,
        reader: &mut JsonStreamReader<BufReader<File>>,
        element_node: &ProjectionNode<'s>,
    ) -> Result<(), JsonStreamError> {
        reader.begin_array()?;

        let start_offset = self.get_current_list_offset();

        while reader.has_next()? {
            self.parse_value(reader, element_node)?;
        }

        let end_offset = self.get_current_list_offset();
        let length = end_offset - start_offset;

        self.set_list_entry(start_offset, length)?;

        reader.end_array()?;
        Ok(())
    }

    fn parse_primitive_value(
        &mut self,
        reader: &mut JsonStreamReader<BufReader<File>>,
        col_idx: usize,
        logical_type: LogicalTypeId,
    ) -> Result<(), JsonStreamError> {
        let row_idx = self.chunk_builder.rows_count;

        match reader.peek()? {
            ValueType::String => {
                let value = reader.next_string()?;
                self.add_string_value(col_idx, row_idx, value)?;
            }
            ValueType::Number => {
                let number_str = reader.next_number_as_string()?;
                match logical_type {
                    LogicalTypeId::Bigint => {
                        if let Ok(value) = number_str.parse::<i64>() {
                            self.add_integer_value(col_idx, row_idx, value)?;
                        } else {
                            self.add_null_value(col_idx, row_idx)?;
                        }
                    }
                    LogicalTypeId::Double => {
                        if let Ok(value) = number_str.parse::<f64>() {
                            self.add_double_value(col_idx, row_idx, value)?;
                        } else {
                            self.add_null_value(col_idx, row_idx)?;
                        }
                    }
                    _ => {
                        // Fallback to string
                        self.add_string_value(col_idx, row_idx, number_str)?;
                    }
                }
            }
            ValueType::Boolean => {
                let value = reader.next_bool()?;
                self.add_boolean_value(col_idx, row_idx, value)?;
            }
            ValueType::Null => {
                reader.next_null()?;
                self.add_null_value(col_idx, row_idx)?;
            }
            _ => {
                // Object or array where primitive expected
                reader.skip_value()?;
                self.add_null_value(col_idx, row_idx)?;
            }
        }
        Ok(())
    }

    // Helper methods for adding values to chunk builder
    fn add_string_value(&mut self, col_idx: usize, row_idx: usize, value: String) -> Result<(), JsonStreamError> {
        if let Some(ColBuilder::String { data, validity }) = self.chunk_builder.columns.get_mut(col_idx) {
            data.push(value);
            validity.set_valid(row_idx);
        }
        Ok(())
    }

    fn add_integer_value(&mut self, col_idx: usize, row_idx: usize, value: i64) -> Result<(), JsonStreamError> {
        if let Some(ColBuilder::Integer { data, validity }) = self.chunk_builder.columns.get_mut(col_idx) {
            data.push(value);
            validity.set_valid(row_idx);
        }
        Ok(())
    }

    fn add_double_value(&mut self, col_idx: usize, row_idx: usize, value: f64) -> Result<(), JsonStreamError> {
        if let Some(ColBuilder::Double { data, validity }) = self.chunk_builder.columns.get_mut(col_idx) {
            data.push(value);
            validity.set_valid(row_idx);
        }
        Ok(())
    }

    fn add_boolean_value(&mut self, col_idx: usize, row_idx: usize, value: bool) -> Result<(), JsonStreamError> {
        if let Some(ColBuilder::Boolean { data, validity }) = self.chunk_builder.columns.get_mut(col_idx) {
            data.push(value);
            validity.set_valid(row_idx);
        }
        Ok(())
    }

    fn add_null_value(&mut self, col_idx: usize, row_idx: usize) -> Result<(), JsonStreamError> {
        if let Some(col) = self.chunk_builder.columns.get_mut(col_idx) {
            match col {
                ColBuilder::String { data, validity } => {
                    data.push(String::new());
                    validity.set_null(row_idx);
                }
                ColBuilder::Integer { data, validity } => {
                    data.push(0);
                    validity.set_null(row_idx);
                }
                ColBuilder::Double { data, validity } => {
                    data.push(0.0);
                    validity.set_null(row_idx);
                }
                ColBuilder::Boolean { data, validity } => {
                    data.push(false);
                    validity.set_null(row_idx);
                }
                _ => {} // Handle list/struct nulls differently
            }
        }
        Ok(())
    }

    fn flush_chunk(&mut self) -> Result<ChunkBuilder, JsonStreamError> {
        let mut new_chunk = ChunkBuilder::new(
            self.chunk_builder.rows_capacity,
            &JsonSchema::Primitive(PrimitiveType::String), // Placeholder
        );
        std::mem::swap(&mut self.chunk_builder, &mut new_chunk);
        Ok(new_chunk)
    }

    // Placeholder methods for list handling
    fn get_current_list_offset(&self) -> u32 { 0 }
    fn set_list_entry(&mut self, _offset: u32, _length: u32) -> Result<(), JsonStreamError> { Ok(()) }
}
```

## 8. Vector Population Implementation

### 8.1 Vector Populator

```rust
pub struct VectorPopulator;

impl VectorPopulator {
    pub fn populate_chunk(
        chunk_builder: &ChunkBuilder,
        output: &mut DataChunkHandle,
    ) -> Result<(), JsonStreamError> {
        output.set_len(chunk_builder.rows_count);

        for (col_idx, col_builder) in chunk_builder.columns.iter().enumerate() {
            Self::populate_column(col_builder, output, col_idx)?;
        }

        Ok(())
    }

    fn populate_column(
        col_builder: &ColBuilder,
        output: &mut DataChunkHandle,
        col_idx: usize,
    ) -> Result<(), JsonStreamError> {
        match col_builder {
            ColBuilder::String { data, validity } => {
                let mut vector = output.flat_vector(col_idx);
                Self::populate_string_vector(&mut vector, data, validity)?;
            }
            ColBuilder::Integer { data, validity } => {
                let mut vector = output.flat_vector(col_idx);
                Self::populate_integer_vector(&mut vector, data, validity)?;
            }
            ColBuilder::Double { data, validity } => {
                let mut vector = output.flat_vector(col_idx);
                Self::populate_double_vector(&mut vector, data, validity)?;
            }
            ColBuilder::Boolean { data, validity } => {
                let mut vector = output.flat_vector(col_idx);
                Self::populate_boolean_vector(&mut vector, data, validity)?;
            }
            ColBuilder::List { offsets, elements, validity } => {
                // List vector population is complex and may require workarounds
                let mut vector = output.flat_vector(col_idx);
                Self::populate_list_as_string(&mut vector, offsets, elements, validity)?;
            }
            ColBuilder::Struct { children, validity } => {
                // Struct vector population requires child vector handling
                let mut vector = output.flat_vector(col_idx);
                Self::populate_struct_as_string(&mut vector, children, validity)?;
            }
        }
        Ok(())
    }

    fn populate_string_vector(
        vector: &mut duckdb::core::FlatVector,
        data: &[String],
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        for (i, value) in data.iter().enumerate() {
            if validity.is_valid(i) {
                let c_string = std::ffi::CString::new(value.as_str())
                    .map_err(|_| JsonStreamError::Schema("Invalid string data".to_string()))?;
                vector.insert(i, c_string);
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }

    fn populate_integer_vector(
        vector: &mut duckdb::core::FlatVector,
        data: &[i64],
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // Workaround: Convert to string until proper integer vector support
        for (i, &value) in data.iter().enumerate() {
            if validity.is_valid(i) {
                vector.insert(i, value.to_string().as_str());
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }

    fn populate_double_vector(
        vector: &mut duckdb::core::FlatVector,
        data: &[f64],
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // Workaround: Convert to string until proper double vector support
        for (i, &value) in data.iter().enumerate() {
            if validity.is_valid(i) {
                vector.insert(i, value.to_string().as_str());
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }

    fn populate_boolean_vector(
        vector: &mut duckdb::core::FlatVector,
        data: &[bool],
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // Workaround: Convert to string until proper boolean vector support
        for (i, &value) in data.iter().enumerate() {
            if validity.is_valid(i) {
                let bool_str = if value { "true" } else { "false" };
                vector.insert(i, bool_str);
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }

    fn populate_list_as_string(
        vector: &mut duckdb::core::FlatVector,
        _offsets: &[(u32, u32)],
        _elements: &ColBuilder,
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // Placeholder: Convert list to JSON string representation
        for i in 0..validity.len {
            if validity.is_valid(i) {
                vector.insert(i, "[list_placeholder]");
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }

    fn populate_struct_as_string(
        vector: &mut duckdb::core::FlatVector,
        _children: &[ColBuilder],
        validity: &Bitmap,
    ) -> Result<(), JsonStreamError> {
        // Placeholder: Convert struct to JSON string representation
        for i in 0..validity.len {
            if validity.is_valid(i) {
                vector.insert(i, "{struct_placeholder}");
            } else {
                vector.set_null(i);
            }
        }
        Ok(())
    }
}
```

## 9. Error Handling Strategy

### 9.1 Error Types

```rust
use thiserror::Error;

#[derive(Error, Debug)]
pub enum JsonStreamError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("JSON parsing error: {0}")]
    Json(#[from] struson::reader::ReaderError),

    #[error("Schema error: {0}")]
    Schema(String),

    #[error("Type mismatch: expected {expected}, found {found}")]
    TypeMismatch { expected: String, found: String },

    #[error("DuckDB error: {0}")]
    DuckDB(String),

    #[error("Memory allocation error: {0}")]
    Memory(String),
}

// Convert to DuckDB error
impl From<JsonStreamError> for Box<dyn std::error::Error> {
    fn from(err: JsonStreamError) -> Self {
        Box::new(err)
    }
}
```

### 9.2 Error Handling Policy

| Scenario | Action | Rationale |
|----------|--------|-----------|
| File not found | Return error immediately | Cannot proceed without input |
| Malformed JSON syntax | Return error immediately | Indicates corrupted data |
| Type mismatch (strict mode) | Return error | User expects type safety |
| Type mismatch (lenient mode) | Insert NULL, continue | Graceful degradation |
| Missing projected field | Insert NULL, continue | Common in real-world JSON |
| Memory allocation failure | Return error immediately | Cannot continue safely |

## 10. Memory Management and Optimization

### 10.1 Memory Usage Targets

- **Peak RAM**: ≤ `chunk_rows × avg_row_size × 2` (double buffering)
- **Buffer reuse**: Clear and reuse `Vec` capacity after chunk flush
- **List growth**: Exponential capacity doubling for list child vectors
- **String interning**: Consider for repeated string values

### 10.2 Buffer Management

```rust
impl ChunkBuilder {
    pub fn clear_but_retain_capacity(&mut self) {
        self.rows_count = 0;
        for col in &mut self.columns {
            col.clear_but_retain_capacity();
        }
    }
}

impl ColBuilder {
    pub fn clear_but_retain_capacity(&mut self) {
        match self {
            ColBuilder::String { data, validity } => {
                data.clear(); // Retains capacity
                validity.clear();
            }
            ColBuilder::List { offsets, elements, validity } => {
                offsets.clear();
                elements.clear_but_retain_capacity();
                validity.clear();
            }
            // ... other variants
        }
    }

    pub fn ensure_list_capacity(&mut self, additional: usize) {
        if let ColBuilder::List { elements, .. } = self {
            // Implement exponential growth for list elements
            elements.ensure_capacity(additional);
        }
    }
}
```

### 10.3 Capacity Management

```rust
const INITIAL_LIST_CAPACITY: usize = 64;
const MAX_GROWTH_FACTOR: f64 = 2.0;
const MAX_SINGLE_ALLOCATION: usize = 1024 * 1024; // 1MB elements

fn calculate_new_capacity(current: usize, required: usize) -> usize {
    let doubled = current.saturating_mul(2);
    let needed = current.saturating_add(required);

    if needed <= doubled && doubled <= MAX_SINGLE_ALLOCATION {
        doubled
    } else {
        needed.min(MAX_SINGLE_ALLOCATION)
    }
}
```

## 11. Testing Infrastructure

### 11.1 Test File Structure

```
tests/
├── json_stream.test              # Main test suite
├── data/
│   ├── simple.json              # Basic object test
│   ├── nested.json              # Deep nesting test
│   ├── large_array.json         # Memory test
│   ├── mixed_types.json         # Type handling test
│   └── malformed.json           # Error handling test
└── expected/
    ├── simple_result.txt
    └── nested_result.txt
```

### 11.2 Test Cases

```sql
# tests/json_stream.test

# Test 1: Basic functionality
statement ok
CREATE TABLE simple_test AS SELECT * FROM json_stream('tests/data/simple.json');

query III
SELECT name, age, active FROM simple_test ORDER BY name;
----
Alice	30	true
Bob	25	false
Charlie	35	true

# Test 2: Projection pushdown
query I
SELECT name FROM json_stream('tests/data/simple.json');
----
Alice
Bob
Charlie

# Test 3: Nested structures
statement ok
CREATE TABLE nested_test AS SELECT * FROM json_stream('tests/data/nested.json');

query II
SELECT user.name, user.profile.age FROM nested_test;
----
Alice	30
Bob	25

# Test 4: Error handling
statement error
SELECT * FROM json_stream('tests/data/malformed.json');
----
JSON parsing error

# Test 5: Large file memory test
statement ok
SELECT COUNT(*) FROM json_stream('tests/data/large_array.json');
```

### 11.3 Test Data Generation

```rust
// Helper for generating test data
pub fn generate_test_json(path: &str, config: TestConfig) {
    match config {
        TestConfig::Simple => {
            let data = json!([
                {"name": "Alice", "age": 30, "active": true},
                {"name": "Bob", "age": 25, "active": false},
                {"name": "Charlie", "age": 35, "active": true}
            ]);
            std::fs::write(path, data.to_string()).unwrap();
        }
        TestConfig::Nested => {
            let data = json!([
                {
                    "user": {
                        "name": "Alice",
                        "profile": {"age": 30, "city": "NYC"}
                    }
                },
                {
                    "user": {
                        "name": "Bob",
                        "profile": {"age": 25, "city": "LA"}
                    }
                }
            ]);
            std::fs::write(path, data.to_string()).unwrap();
        }
        TestConfig::LargeArray => {
            // Generate large JSON array for memory testing
            generate_large_json_array(path, 100_000);
        }
    }
}
```

## 12. Performance Validation

### 12.1 Benchmarking Setup

```bash
# Memory usage validation
/usr/bin/time -l duckdb -c "SELECT COUNT(*) FROM json_stream('large.json');"

# Throughput measurement
hyperfine --warmup 3 --runs 6 \
  "duckdb -c 'SELECT SUM(value) FROM json_stream(\"data.json\");'" \
  "duckdb -c 'SELECT SUM(value) FROM read_json_auto(\"data.json\");'"

# Projection pushdown validation
hyperfine --warmup 3 --runs 6 \
  "duckdb -c 'SELECT name FROM json_stream(\"data.json\");'" \
  "duckdb -c 'SELECT * FROM json_stream(\"data.json\");'"
```

### 12.2 Performance Targets

| Metric | Target | Measurement Method |
|--------|--------|--------------------|
| Peak Memory | ≤ 2 × chunk_size × avg_row_size | `/usr/bin/time -l` |
| Single-thread throughput | ≥ 50 MiB/s | `hyperfine` with large files |
| Projection speedup | ≥ 2x for 50% column reduction | Compare full vs partial SELECT |
| Startup overhead | ≤ 100ms | Time to first row |

### 12.3 Memory Profiling

```rust
#[cfg(feature = "memory-profiling")]
mod profiling {
    use std::alloc::{GlobalAlloc, Layout, System};
    use std::sync::atomic::{AtomicUsize, Ordering};

    static ALLOCATED: AtomicUsize = AtomicUsize::new(0);

    struct TrackingAllocator;

    unsafe impl GlobalAlloc for TrackingAllocator {
        unsafe fn alloc(&self, layout: Layout) -> *mut u8 {
            let ret = System.alloc(layout);
            if !ret.is_null() {
                ALLOCATED.fetch_add(layout.size(), Ordering::SeqCst);
            }
            ret
        }

        unsafe fn dealloc(&self, ptr: *mut u8, layout: Layout) {
            System.dealloc(ptr, layout);
            ALLOCATED.fetch_sub(layout.size(), Ordering::SeqCst);
        }
    }

    #[global_allocator]
    static GLOBAL: TrackingAllocator = TrackingAllocator;

    pub fn current_memory_usage() -> usize {
        ALLOCATED.load(Ordering::SeqCst)
    }
}
```

## 13. Implementation Checklist

### 13.1 Core Components

- [ ] **JsonSchema enum** with Primitive/Struct/List variants
- [ ] **ProjectionNode enum** with skip-aware logic
- [ ] **Bitmap implementation** for validity tracking
- [ ] **ColBuilder enum** for all supported types
- [ ] **ChunkBuilder struct** with capacity management
- [ ] **SchemaDetector** using struson streaming API
- [ ] **ProjectionTreeBuilder** for column selection
- [ ] **StreamingJsonParser** with skip logic
- [ ] **VectorPopulator** for DuckDB vector conversion

### 13.2 DuckDB Integration

- [ ] **VTab trait implementation** with correct lifecycle
- [ ] **Bind data structure** with `#[repr(C)]`
- [ ] **Init data structure** with `#[repr(C)]`
- [ ] **Extension entry point** with proper registration
- [ ] **Column type mapping** from JsonSchema to LogicalTypeId
- [ ] **Error conversion** to DuckDB error format

### 13.3 Memory Management

- [ ] **Buffer reuse** after chunk flush
- [ ] **Exponential growth** for list child vectors
- [ ] **Memory usage tracking** and validation
- [ ] **Capacity management** with reasonable limits
- [ ] **String interning** for repeated values (optional)

### 13.4 Error Handling

- [ ] **Comprehensive error types** with thiserror
- [ ] **Graceful degradation** for type mismatches
- [ ] **Proper error propagation** to DuckDB
- [ ] **Resource cleanup** on error paths
- [ ] **Validation of input parameters**

### 13.5 Testing

- [ ] **Basic functionality tests** with simple JSON
- [ ] **Projection pushdown tests** with column selection
- [ ] **Nested structure tests** with deep objects
- [ ] **Large file tests** for memory validation
- [ ] **Error handling tests** with malformed JSON
- [ ] **Performance benchmarks** vs read_json_auto

## 14. Known Limitations and Workarounds

### 14.1 DuckDB Rust API Limitations

| Limitation | Workaround | Future Solution |
|------------|------------|-----------------|
| Limited vector type support | Convert to strings initially | Wait for expanded API |
| No direct LIST vector creation | Serialize to JSON string | Implement via FFI |
| No direct STRUCT vector creation | Flatten to multiple columns | Implement via FFI |
| Complex validity bitmap handling | Use custom Bitmap struct | Use DuckDB's bitmap API |

### 14.2 Struson API Considerations

| Issue | Solution |
|-------|---------|
| Cannot peek after consuming | Careful state management |
| No random access | Stream-only processing |
| Limited error context | Wrap errors with position info |
| Memory usage for large strings | Consider streaming string processing |

### 14.3 Performance Considerations

- **Initial implementation priority**: Correctness over performance
- **String conversion overhead**: Acceptable for MVP, optimize later
- **Memory usage**: May exceed targets initially due to safety overhead
- **Parallel processing**: Implement after single-threaded version works

## 4. Complete Architecture Overview

### 4.1 Core Data Structures

```rust
#[derive(Debug, Clone, PartialEq)]
pub enum PrimitiveType {
    Boolean,
    Integer,
    Double,
    String,
}

#[derive(Debug, Clone)]
pub enum JsonSchema {
    Primitive(PrimitiveType),
    Struct { 
        fields: IndexMap<String, JsonSchema> 
    },
    List { 
        element: Box<JsonSchema> 
    },
}

#[derive(Debug, Clone)]
pub enum ProjectionNode<'s> {
    Leaf { 
        col_idx: usize,
        logical_type: LogicalTypeId,
    },
    Struct { 
        children: HashMap<&'s str, ProjectionNode<'s>>, 
        any_projected: bool 
    },
    List { 
        element: Box<ProjectionNode<'s>>, 
        required: bool 
    },
}
```
